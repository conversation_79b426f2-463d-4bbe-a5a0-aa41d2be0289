#!/usr/bin/env python3
"""
Test script to verify teacher-specific data filtering is working correctly.
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gui.services.auth_service import AuthService
from gui.services.class_service import get_existing_classes, create_class
from gui.services.subject_service import get_class_subjects, create_class_subject
from quiz_management.services.quiz_service import get_quizzes, create_quiz

def test_teacher_filtering():
    """Test that teachers can only see their own data"""
    print("🧪 Testing Teacher-Specific Data Filtering")
    print("=" * 50)

    auth_service = AuthService()

    # Create two test teachers
    print("👨‍🏫 Creating test teachers...")

    teacher1_created = auth_service.create_teacher(
        username="teacher1",
        password="test123",
        full_name="Teacher One",
        email="<EMAIL>"
    )

    teacher2_created = auth_service.create_teacher(
        username="teacher2",
        password="test123",
        full_name="Teacher Two",
        email="<EMAIL>"
    )

    if not teacher1_created:
        print("⚠️  Teacher1 already exists, continuing...")
    if not teacher2_created:
        print("⚠️  Teacher2 already exists, continuing...")

    # Get teacher IDs
    teacher1 = auth_service.authenticate("teacher1", "test123")
    teacher2 = auth_service.authenticate("teacher2", "test123")

    if not teacher1 or not teacher2:
        print("❌ Failed to authenticate test teachers")
        return False

    teacher1_id = teacher1['id']
    teacher2_id = teacher2['id']

    print(f"✅ Teacher1 ID: {teacher1_id}")
    print(f"✅ Teacher2 ID: {teacher2_id}")

    # Create test data for each teacher
    print("\n📚 Creating test classes...")

    # Teacher 1 creates classes
    result1 = create_class("Math Class T1", "Math class for teacher 1", teacher1_id)
    result2 = create_class("Science Class T1", "Science class for teacher 1", teacher1_id)
    print(f"Teacher1 class creation results: {result1}, {result2}")

    # Teacher 2 creates classes
    result3 = create_class("Math Class T2", "Math class for teacher 2", teacher2_id)
    result4 = create_class("History Class T2", "History class for teacher 2", teacher2_id)
    print(f"Teacher2 class creation results: {result3}, {result4}")

    # Test class filtering
    print("\n🔍 Testing class filtering...")

    all_classes = get_existing_classes()
    teacher1_classes = get_existing_classes(teacher1_id)
    teacher2_classes = get_existing_classes(teacher2_id)

    print(f"Total classes: {len(all_classes)}")
    print(f"Teacher1 classes: {len(teacher1_classes)} - {list(teacher1_classes.keys())}")
    print(f"Teacher2 classes: {len(teacher2_classes)} - {list(teacher2_classes.keys())}")

    # Verify teachers only see their own classes
    teacher1_class_names = set(teacher1_classes.keys())
    teacher2_class_names = set(teacher2_classes.keys())

    if "Math Class T2" in teacher1_class_names or "History Class T2" in teacher1_class_names:
        print("❌ Teacher1 can see Teacher2's classes!")
        return False

    if "Math Class T1" in teacher2_class_names or "Science Class T1" in teacher2_class_names:
        print("❌ Teacher2 can see Teacher1's classes!")
        return False

    print("✅ Class filtering working correctly!")

    # Create test subjects
    print("\n📖 Creating test subjects...")

    # Get class IDs
    if "Math Class T1" not in teacher1_classes:
        print("❌ Math Class T1 not found in teacher1_classes!")
        print(f"Available classes: {list(teacher1_classes.keys())}")
        return False
    if "Math Class T2" not in teacher2_classes:
        print("❌ Math Class T2 not found in teacher2_classes!")
        print(f"Available classes: {list(teacher2_classes.keys())}")
        return False

    teacher1_math_class_id = teacher1_classes["Math Class T1"]["id"]
    teacher2_math_class_id = teacher2_classes["Math Class T2"]["id"]

    # Teacher 1 creates subjects
    create_class_subject(teacher1_math_class_id, "Algebra", "", "", teacher1_id)
    create_class_subject(teacher1_math_class_id, "Geometry", "", "", teacher1_id)

    # Teacher 2 creates subjects
    create_class_subject(teacher2_math_class_id, "Calculus", "", "", teacher2_id)
    create_class_subject(teacher2_math_class_id, "Statistics", "", "", teacher2_id)

    # Test subject filtering
    print("\n🔍 Testing subject filtering...")

    all_subjects = get_class_subjects()
    teacher1_subjects = get_class_subjects(teacher_id=teacher1_id)
    teacher2_subjects = get_class_subjects(teacher_id=teacher2_id)

    print(f"Total subjects: {len(all_subjects)}")
    print(f"Teacher1 subjects: {len(teacher1_subjects)} - {[s['subject_name'] for s in teacher1_subjects]}")
    print(f"Teacher2 subjects: {len(teacher2_subjects)} - {[s['subject_name'] for s in teacher2_subjects]}")

    # Verify teachers only see their own subjects
    teacher1_subject_names = {s['subject_name'] for s in teacher1_subjects}
    teacher2_subject_names = {s['subject_name'] for s in teacher2_subjects}

    if "Calculus" in teacher1_subject_names or "Statistics" in teacher1_subject_names:
        print("❌ Teacher1 can see Teacher2's subjects!")
        return False

    if "Algebra" in teacher2_subject_names or "Geometry" in teacher2_subject_names:
        print("❌ Teacher2 can see Teacher1's subjects!")
        return False

    print("✅ Subject filtering working correctly!")

    # Create test quizzes
    print("\n📝 Creating test quizzes...")

    # Get subject IDs
    teacher1_algebra_subject = next((s for s in teacher1_subjects if s['subject_name'] == 'Algebra'), None)
    teacher2_calculus_subject = next((s for s in teacher2_subjects if s['subject_name'] == 'Calculus'), None)

    if teacher1_algebra_subject and teacher2_calculus_subject:
        # Teacher 1 creates quiz
        create_quiz("Algebra Quiz 1", "Basic algebra quiz", teacher1_math_class_id, teacher1_algebra_subject['id'], teacher1_id)

        # Teacher 2 creates quiz
        create_quiz("Calculus Quiz 1", "Basic calculus quiz", teacher2_math_class_id, teacher2_calculus_subject['id'], teacher2_id)

        # Test quiz filtering
        print("\n🔍 Testing quiz filtering...")

        all_quizzes = get_quizzes()
        teacher1_quizzes = get_quizzes(teacher_id=teacher1_id)
        teacher2_quizzes = get_quizzes(teacher_id=teacher2_id)

        print(f"Total quizzes: {len(all_quizzes)}")
        print(f"Teacher1 quizzes: {len(teacher1_quizzes)} - {[q['title'] for q in teacher1_quizzes]}")
        print(f"Teacher2 quizzes: {len(teacher2_quizzes)} - {[q['title'] for q in teacher2_quizzes]}")

        # Verify teachers only see their own quizzes
        teacher1_quiz_titles = {q['title'] for q in teacher1_quizzes}
        teacher2_quiz_titles = {q['title'] for q in teacher2_quizzes}

        if "Calculus Quiz 1" in teacher1_quiz_titles:
            print("❌ Teacher1 can see Teacher2's quizzes!")
            return False

        if "Algebra Quiz 1" in teacher2_quiz_titles:
            print("❌ Teacher2 can see Teacher1's quizzes!")
            return False

        print("✅ Quiz filtering working correctly!")

    # Test admin access
    print("\n👑 Testing admin access...")

    admin = auth_service.authenticate("admin", "admin")
    if admin:
        admin_classes = get_existing_classes()  # Admin should see all
        admin_subjects = get_class_subjects()   # Admin should see all
        admin_quizzes = get_quizzes()          # Admin should see all

        print(f"Admin sees {len(admin_classes)} classes (should see all)")
        print(f"Admin sees {len(admin_subjects)} subjects (should see all)")
        print(f"Admin sees {len(admin_quizzes)} quizzes (should see all)")

        # Admin should see more than individual teachers
        if len(admin_classes) >= len(teacher1_classes) and len(admin_classes) >= len(teacher2_classes):
            print("✅ Admin can see all data!")
        else:
            print("❌ Admin cannot see all data!")
            return False

    print("\n🎉 All tests passed! Teacher-specific data filtering is working correctly!")
    return True

if __name__ == "__main__":
    success = test_teacher_filtering()
    if not success:
        print("\n💥 Some tests failed!")
        sys.exit(1)
    else:
        print("\n✅ All tests passed successfully!")
