"""
Teacher management view for admin.
"""
import flet as ft
from gui.components.admin_layout import create_admin_page_layout
from gui.components.dialogs import create_confirmation_dialog, create_form_dialog, show_dialog, close_dialog
from gui.services.auth_service import AuthService
from gui.config.constants import ROUTE_LOGIN, ROUTE_ADMIN_DASHBOARD, ICON_ADD
from gui.config.language import get_text

def create_admin_teachers_view(page: ft.Page):
    """Create the teacher management view."""
    auth_service = AuthService()
    current_language = getattr(page, 'language', 'en')
    is_mobile = getattr(page, 'is_mobile', False)

    # Check if user is admin
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user or current_user.get('role') != 'admin':
        page.go(ROUTE_LOGIN)
        return ft.View(route="/admin/teachers", controls=[])

    # Get initial teachers data
    teachers = auth_service.get_all_teachers()

    # Modern welcome section with gradient background and French text
    welcome_section = ft.Container(
        content=ft.Column([
            ft.Row([
                ft.Icon(
                    ft.Icons.PEOPLE,
                    size=36,
                    color=ft.Colors.WHITE
                ),
                ft.Text(
                    "Gestion des Enseignants",
                    size=32,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.WHITE,
                    text_align=ft.TextAlign.CENTER
                )
            ], alignment=ft.MainAxisAlignment.CENTER, spacing=12),
            ft.Text(
                f"{len(teachers)} enseignant{'s' if len(teachers) > 1 else ''} dans le système",
                size=18,
                color=ft.Colors.WHITE70,
                text_align=ft.TextAlign.CENTER,
                weight=ft.FontWeight.W_400
            )
        ],
        spacing=12,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        padding=ft.padding.all(28),
        margin=ft.margin.only(bottom=24, top=0),
        gradient=ft.LinearGradient(
            begin=ft.alignment.top_left,
            end=ft.alignment.bottom_right,
            colors=[ft.Colors.BLUE_700, ft.Colors.INDIGO_600, ft.Colors.PURPLE_600]
        ),
        border_radius=ft.border_radius.all(24),
        alignment=ft.alignment.center,
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=20,
            color=ft.Colors.with_opacity(0.3, ft.Colors.BLUE_600),
            offset=ft.Offset(0, 8)
        )
    )

    # Create a modern column for teacher cards
    teachers_container = ft.Column(
        spacing=16,
        width=page.width*0.9 if is_mobile else 600,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER
    )

    # Store reference to containers for dynamic updates
    page._teachers_container = teachers_container

    def refresh_teachers():
        """Refresh the teachers list."""
        teachers_container.controls.clear()
        fresh_teachers = auth_service.get_all_teachers()

        # Update welcome section with teacher count
        welcome_section.content.controls[1].value = f"{len(fresh_teachers)} enseignant{'s' if len(fresh_teachers) > 1 else ''} dans le système"

        # Update no teachers message visibility
        no_teachers_message.visible = len(fresh_teachers) == 0

        if len(fresh_teachers) == 0:
            page.update()
            return

        # Create teacher cards
        for teacher in fresh_teachers:
            teacher_card = create_teacher_card(teacher)
            teachers_container.controls.append(teacher_card)

        page.update()

    def create_teacher_card(teacher: dict):
        """Create a teacher card with modern styling and clickable functionality."""
        status_color = ft.Colors.GREEN_600 if teacher['is_active'] else ft.Colors.RED_600
        status_text = "Actif" if teacher['is_active'] else "Inactif"
        status_bg_color = ft.Colors.GREEN_50 if teacher['is_active'] else ft.Colors.RED_50

        def create_toggle_handler(teacher_data):
            def toggle_status(e):
                e.control.parent.parent.parent.parent.parent.update()  # Stop event propagation
                new_status = not teacher_data['is_active']
                if auth_service.update_teacher_status(teacher_data['id'], new_status):
                    refresh_teachers()
                    page.app_state.show_success(f"Statut de l'enseignant mis à jour: {'Actif' if new_status else 'Inactif'}")
            return toggle_status

        def create_delete_handler(teacher_data):
            def delete_teacher(e):
                e.control.parent.parent.parent.parent.parent.update()  # Stop event propagation
                def confirm_delete():
                    if auth_service.delete_teacher(teacher_data['id']):
                        refresh_teachers()
                        page.app_state.show_success("Enseignant supprimé avec succès")

                dialog = create_confirmation_dialog(
                    page,
                    "Confirmer la Suppression",
                    f"Êtes-vous sûr de vouloir supprimer l'enseignant '{teacher_data['full_name']}'?",
                    confirm_delete,
                    confirm_text="Supprimer",
                    is_destructive=True
                )
                show_dialog(page, dialog)
            return delete_teacher

        def card_click(_):
            """Handle card click - show teacher details."""
            page.app_state.show_success(f"Enseignant sélectionné: {teacher['full_name']}")

        return ft.Container(
            content=ft.Column([
                # Header with avatar and name
                ft.Row([
                    ft.Container(
                        content=ft.Icon(
                            ft.Icons.PERSON,
                            size=24,
                            color=ft.Colors.BLUE_600
                        ),
                        width=48,
                        height=48,
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=24,
                        alignment=ft.alignment.center
                    ),
                    ft.Column([
                        ft.Text(
                            teacher['full_name'],
                            size=18,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.BLUE_900
                        ),
                        ft.Container(
                            content=ft.Text(
                                status_text,
                                color=status_color,
                                size=13,
                                weight=ft.FontWeight.W_600
                            ),
                            bgcolor=status_bg_color,
                            padding=ft.padding.symmetric(horizontal=12, vertical=4),
                            border_radius=12,
                        )
                    ], spacing=4, expand=True),
                    ft.Column([
                        ft.Container(
                            content=ft.IconButton(
                                icon=ft.Icons.TOGGLE_ON if teacher['is_active'] else ft.Icons.TOGGLE_OFF,
                                icon_color=status_color,
                                tooltip="Changer le Statut",
                                bgcolor=ft.Colors.WHITE,
                                icon_size=20,
                                on_click=create_toggle_handler(teacher)
                            ),
                            shadow=ft.BoxShadow(
                                spread_radius=0,
                                blur_radius=4,
                                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                                offset=ft.Offset(0, 2)
                            ),
                            border_radius=20
                        ),
                        ft.Container(
                            content=ft.IconButton(
                                icon=ft.Icons.DELETE_OUTLINE,
                                icon_color=ft.Colors.RED_500,
                                tooltip="Supprimer l'Enseignant",
                                bgcolor=ft.Colors.RED_50,
                                icon_size=20,
                                on_click=create_delete_handler(teacher)
                            ),
                            shadow=ft.BoxShadow(
                                spread_radius=0,
                                blur_radius=4,
                                color=ft.Colors.with_opacity(0.1, ft.Colors.RED_300),
                                offset=ft.Offset(0, 2)
                            ),
                            border_radius=20
                        )
                    ], spacing=8)
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN, spacing=12),

                ft.Divider(height=1, color=ft.Colors.GREY_200),

                # Details section
                ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.ACCOUNT_CIRCLE, size=16, color=ft.Colors.GREY_600),
                        ft.Text(f"Nom d'utilisateur: {teacher['username']}", size=14, color=ft.Colors.GREY_700)
                    ], spacing=8),
                    ft.Row([
                        ft.Icon(ft.Icons.EMAIL, size=16, color=ft.Colors.GREY_600),
                        ft.Text(f"Email: {teacher['email'] or 'Non fourni'}", size=14, color=ft.Colors.GREY_700)
                    ], spacing=8),
                    ft.Row([
                        ft.Icon(ft.Icons.CALENDAR_TODAY, size=16, color=ft.Colors.GREY_600),
                        ft.Text(f"Créé le: {teacher['created_at'][:10]}", size=14, color=ft.Colors.GREY_700)
                    ], spacing=8)
                ], spacing=6)
            ], spacing=16),
            padding=ft.padding.all(24),
            bgcolor=ft.Colors.WHITE,
            border_radius=ft.border_radius.all(20),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=12,
                color=ft.Colors.with_opacity(0.08, ft.Colors.BLACK),
                offset=ft.Offset(0, 4)
            ),
            margin=ft.margin.all(8),
            on_click=card_click,
            ink=True,
            border=ft.border.all(1, ft.Colors.BLUE_100),
            animate=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT)
        )

    def show_add_teacher_dialog(_):
        """Show add teacher dialog."""
        username_field = ft.TextField(
            label="Nom d'utilisateur",
            hint_text="Entrez le nom d'utilisateur",
            border_radius=12,
            content_padding=ft.padding.symmetric(horizontal=16, vertical=12),
            border_color=ft.Colors.BLUE_200,
            focused_border_color=ft.Colors.BLUE_600,
        )
        password_field = ft.TextField(
            label="Mot de passe",
            hint_text="Entrez le mot de passe",
            password=True,
            border_radius=12,
            content_padding=ft.padding.symmetric(horizontal=16, vertical=12),
            border_color=ft.Colors.BLUE_200,
            focused_border_color=ft.Colors.BLUE_600,
        )
        full_name_field = ft.TextField(
            label="Nom complet",
            hint_text="Entrez le nom complet",
            border_radius=12,
            content_padding=ft.padding.symmetric(horizontal=16, vertical=12),
            border_color=ft.Colors.BLUE_200,
            focused_border_color=ft.Colors.BLUE_600,
        )
        email_field = ft.TextField(
            label="Email (Optionnel)",
            hint_text="Entrez l'email (optionnel)",
            border_radius=12,
            content_padding=ft.padding.symmetric(horizontal=16, vertical=12),
            border_color=ft.Colors.BLUE_200,
            focused_border_color=ft.Colors.BLUE_600,
        )
        phone_field = ft.TextField(
            label="Téléphone (Optionnel)",
            hint_text="Entrez le téléphone (optionnel)",
            border_radius=12,
            content_padding=ft.padding.symmetric(horizontal=16, vertical=12),
            border_color=ft.Colors.BLUE_200,
            focused_border_color=ft.Colors.BLUE_600,
        )

        def add_teacher():
            if not all([username_field.value, password_field.value, full_name_field.value]):
                username_field.error_text = "Le nom d'utilisateur est requis" if not username_field.value else ""
                password_field.error_text = "Le mot de passe est requis" if not password_field.value else ""
                full_name_field.error_text = "Le nom complet est requis" if not full_name_field.value else ""
                page.update()
                return

            success = auth_service.create_teacher(
                username=username_field.value.strip(),
                password=password_field.value.strip(),
                full_name=full_name_field.value.strip(),
                email=email_field.value.strip() or None,
                phone=phone_field.value.strip() or None
            )

            if success:
                refresh_teachers()
                page.app_state.show_success("Enseignant créé avec succès")
            else:
                page.app_state.show_error("Échec de la création de l'enseignant. Le nom d'utilisateur existe peut-être déjà.")

        form_controls = [
            username_field,
            password_field,
            full_name_field,
            email_field,
            phone_field,
        ]

        dialog = create_form_dialog(
            page,
            "Ajouter un Nouvel Enseignant",
            form_controls,
            add_teacher,
            submit_text="Ajouter l'Enseignant"
        )
        show_dialog(page, dialog)

    # Add teacher button with modern French styling
    add_teacher_button = ft.ElevatedButton(
        "Ajouter un Enseignant",
        icon=ICON_ADD,
        tooltip="Ajouter un nouvel enseignant",
        on_click=show_add_teacher_dialog,
        style=ft.ButtonStyle(
            bgcolor=ft.Colors.BLUE_600,
            color=ft.Colors.WHITE,
            shape=ft.RoundedRectangleBorder(radius=16),
            padding=ft.padding.symmetric(horizontal=24, vertical=16),
            shadow_color=ft.Colors.BLUE_200,
            elevation=4,
            text_style=ft.TextStyle(size=16, weight=ft.FontWeight.W_600)
        ),
    )

    # Modern add teacher form with French text
    add_teacher_form = ft.Container(
        content=ft.Column([
            ft.Row([
                ft.Icon(ft.Icons.PERSON_ADD, size=24, color=ft.Colors.BLUE_600),
                ft.Text(
                    "Ajouter un Nouvel Enseignant",
                    size=20,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE_900
                )
            ], spacing=12, alignment=ft.MainAxisAlignment.CENTER),
            ft.Text(
                "Créez un nouveau compte enseignant pour votre établissement",
                size=14,
                color=ft.Colors.GREY_600,
                text_align=ft.TextAlign.CENTER
            ),
            add_teacher_button,
        ], spacing=16, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        width=page.width*0.9 if is_mobile else 600,
        padding=ft.padding.all(28),
        margin=ft.margin.only(bottom=24),
        bgcolor=ft.Colors.WHITE,
        border_radius=ft.border_radius.all(20),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=12,
            color=ft.Colors.with_opacity(0.1, ft.Colors.BLUE_600),
            offset=ft.Offset(0, 4)
        ),
        border=ft.border.all(1, ft.Colors.BLUE_100)
    )

    # No teachers message with French text and better design
    no_teachers_message = ft.Container(
        content=ft.Column([
            ft.Container(
                content=ft.Icon(
                    ft.Icons.PERSON_ADD,
                    size=64,
                    color=ft.Colors.BLUE_300
                ),
                width=120,
                height=120,
                bgcolor=ft.Colors.BLUE_50,
                border_radius=60,
                alignment=ft.alignment.center
            ),
            ft.Text(
                "Aucun enseignant pour le moment",
                size=22,
                color=ft.Colors.BLUE_900,
                text_align=ft.TextAlign.CENTER,
                weight=ft.FontWeight.BOLD
            ),
            ft.Text(
                "Ajoutez votre premier enseignant pour commencer",
                size=16,
                color=ft.Colors.GREY_600,
                text_align=ft.TextAlign.CENTER
            )
        ], spacing=20, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        alignment=ft.alignment.center,
        padding=ft.padding.all(48),
        visible=len(teachers) == 0,
        bgcolor=ft.Colors.WHITE,
        border_radius=ft.border_radius.all(20),
        margin=ft.margin.only(bottom=24),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=8,
            color=ft.Colors.with_opacity(0.1, ft.Colors.BLUE_600),
            offset=ft.Offset(0, 4)
        ),
        border=ft.border.all(1, ft.Colors.BLUE_100)
    )

    # Create teacher cards for each existing teacher (initial load)
    for teacher in teachers:
        teacher_card = create_teacher_card(teacher)
        teachers_container.controls.append(teacher_card)

    # Store the refresh function for use in dialogs
    page._refresh_teachers = refresh_teachers

    # Create enhanced content
    content = [
        welcome_section,
        ft.Container(
            content=add_teacher_form,
            alignment=ft.alignment.center,
        ),
        no_teachers_message,
        ft.Container(
            content=teachers_container,
            alignment=ft.alignment.center,
        )
    ]

    return create_admin_page_layout(
        page,
        "Gestion des Enseignants",
        content
    )
