"""
Local database module for offline operation.
Uses SQLite to store data locally instead of Supa<PERSON>.
"""
import sqlite3
import json
import numpy as np
from datetime import datetime
import threading
from facial_recognition_system.config import DB_FILE

# Thread-local storage for database connections
local_storage = threading.local()

def get_connection():
    """Get a thread-local database connection"""
    if not hasattr(local_storage, 'connection'):
        local_storage.connection = sqlite3.connect(DB_FILE)
        # Enable foreign keys
        local_storage.connection.execute('PRAGMA foreign_keys = ON')
        # Configure connection to return rows as dictionaries
        local_storage.connection.row_factory = sqlite3.Row
    return local_storage.connection

def close_connection():
    """Close the thread-local database connection if it exists"""
    if hasattr(local_storage, 'connection'):
        local_storage.connection.close()
        del local_storage.connection

def initialize_database():
    """Create database tables if they don't exist"""
    conn = get_connection()
    cursor = conn.cursor()

    # Create classes table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS classes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        teacher_id INTEGER,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE SET NULL,
        UNIQUE(name, teacher_id)
    )
    ''')

    # Create students table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS etudiants (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        code TEXT NOT NULL,  -- JSON string of face encoding
        class_id INTEGER,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL
    )
    ''')

    # Create subjects table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS matieres (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        class_id INTEGER,
        teacher_id INTEGER,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL,
        FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE SET NULL,
        UNIQUE(name, class_id, teacher_id)
    )
    ''')

    # Create attendance table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS presences (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        student_id INTEGER NOT NULL,
        class_id INTEGER,
        subject_id INTEGER,
        status BOOLEAN NOT NULL,
        date TEXT NOT NULL,
        time TEXT NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (student_id) REFERENCES etudiants(id) ON DELETE CASCADE,
        FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL,
        FOREIGN KEY (subject_id) REFERENCES matieres(id) ON DELETE SET NULL,
        UNIQUE(student_id, date, class_id, subject_id)
    )
    ''')

    conn.commit()
    print("✅ Database initialized successfully")

# Student-related functions
def get_existing_records():
    """Fetch all existing student data from local database"""
    try:
        print("Connecting to local database...")
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT name, code, id FROM etudiants")
        rows = cursor.fetchall()

        existing_data = {row['name']: {'code': json.loads(row['code']), 'id': row['id']} for row in rows}

        return existing_data
    except Exception as e:
        return {}

def add_person_to_database(name, encodings, class_id=None):
    """Add a person's face encoding to the local database"""
    try:
        # Compute average encoding
        avg_enc = np.mean(encodings, axis=0).tolist()

        # Insert into database
        conn = get_connection()
        cursor = conn.cursor()

        now = datetime.now().isoformat()

        # Convert numpy array to JSON string
        encoded_data = json.dumps(avg_enc)

        if class_id is not None:
            cursor.execute(
                "INSERT INTO etudiants (name, code, class_id, created_at) VALUES (?, ?, ?, ?)",
                (name, encoded_data, class_id, now)
            )
        else:
            cursor.execute(
                "INSERT INTO etudiants (name, code, created_at) VALUES (?, ?, ?)",
                (name, encoded_data, now)
            )

        conn.commit()
        return True
    except Exception as e:
        return False

def remove_person_from_database(name):
    """Remove a person from the local database"""
    try:
        # Get existing records to find the person's ID
        existing_data = get_existing_records()

        if name not in existing_data:
            return False

        # Get the person's ID
        person_id = existing_data[name]['id']

        # Delete from database
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute("DELETE FROM etudiants WHERE id = ?", (person_id,))
        conn.commit()

        return True
    except Exception as e:
        return False

def update_person_in_database(name, encodings):
    """Update a person's face encoding in the local database"""
    try:
        # Get existing records to find the person's ID
        existing_data = get_existing_records()

        if name not in existing_data:
            return False

        # Get the person's ID
        person_id = existing_data[name]['id']

        # Compute average encoding
        avg_enc = np.mean(encodings, axis=0).tolist()

        # Update database
        conn = get_connection()
        cursor = conn.cursor()

        now = datetime.now().isoformat()

        # Convert numpy array to JSON string
        encoded_data = json.dumps(avg_enc)

        cursor.execute(
            "UPDATE etudiants SET code = ?, updated_at = ? WHERE id = ?",
            (encoded_data, now, person_id)
        )
        conn.commit()

        return True
    except Exception as e:
        return False

# Class-related functions
def get_existing_classes(teacher_id=None):
    """Fetch existing classes from local database, optionally filtered by teacher"""
    try:
        conn = get_connection()
        cursor = conn.cursor()

        if teacher_id:
            cursor.execute("SELECT * FROM classes WHERE teacher_id = ?", (teacher_id,))
        else:
            cursor.execute("SELECT * FROM classes")

        rows = cursor.fetchall()

        if not rows:
            return {}

        classes = {row['name']: {'id': row['id'], 'created_at': row['created_at'], 'teacher_id': row.get('teacher_id')} for row in rows}

        return classes
    except Exception as e:
        return {}

def create_class(class_name, description="", teacher_id=None):
    """Create a new class in the local database"""
    try:
        # Check if class already exists for this teacher
        existing_classes = get_existing_classes(teacher_id)
        if class_name in existing_classes:
            return False

        # Create new class
        conn = get_connection()
        cursor = conn.cursor()

        now = datetime.now().isoformat()

        cursor.execute(
            "INSERT INTO classes (name, description, teacher_id, created_at) VALUES (?, ?, ?, ?)",
            (class_name, description, teacher_id, now)
        )
        conn.commit()

        return True
    except Exception as e:
        return False

def update_class_name(class_id, new_name):
    """Update the name of a class in the local database"""
    try:
        # Check if the new name already exists
        existing_classes = get_existing_classes()
        if any(data['id'] != class_id and name == new_name for name, data in existing_classes.items()):
            return False

        # Update the class name
        conn = get_connection()
        cursor = conn.cursor()

        now = datetime.now().isoformat()

        cursor.execute(
            "UPDATE classes SET name = ?, updated_at = ? WHERE id = ?",
            (new_name, now, class_id)
        )
        conn.commit()

        return True
    except Exception as e:
        return False

def delete_class(class_id):
    """Delete a class from the local database"""
    try:
        # First, unassign all students from this class
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute("UPDATE etudiants SET class_id = NULL WHERE class_id = ?", (class_id,))

        # Then delete the class
        cursor.execute("DELETE FROM classes WHERE id = ?", (class_id,))
        conn.commit()

        return True
    except Exception as e:
        return False

def get_students_in_class(class_id):
    """Get all students in a specific class"""
    try:
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT name, id FROM etudiants WHERE class_id = ?", (class_id,))
        rows = cursor.fetchall()

        if not rows:
            return []

        students = [(row['name'], row['id']) for row in rows]
        return students
    except Exception as e:
        return []

def assign_student_to_class(student_id, class_id):
    """Assign a student to a class"""
    try:
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute("UPDATE etudiants SET class_id = ? WHERE id = ?", (class_id, student_id))
        conn.commit()

        return True
    except Exception as e:
        return False

def remove_student_from_class(student_id):
    """Remove a student from their class"""
    try:
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute("UPDATE etudiants SET class_id = NULL WHERE id = ?", (student_id,))
        conn.commit()

        return True
    except Exception as e:
        return False

# Subject-related functions
def get_existing_subjects(class_id=None, teacher_id=None):
    """Fetch existing subjects from local database, optionally filtered by class_id and teacher_id"""
    try:
        conn = get_connection()
        cursor = conn.cursor()

        query = "SELECT * FROM matieres"
        params = []

        conditions = []
        if class_id:
            conditions.append("class_id = ?")
            params.append(class_id)
        if teacher_id:
            conditions.append("teacher_id = ?")
            params.append(teacher_id)

        if conditions:
            query += " WHERE " + " AND ".join(conditions)

        cursor.execute(query, params)
        rows = cursor.fetchall()

        if not rows:
            return {}

        subjects = {row['name']: {'id': row['id'], 'created_at': row['created_at'], 'class_id': row['class_id'], 'teacher_id': row.get('teacher_id')}
                   for row in rows}

        return subjects
    except Exception as e:
        return {}

def create_subject(subject_name, class_id=None, description="", teacher_id=None):
    """Create a new subject in the local database"""
    try:
        conn = get_connection()
        cursor = conn.cursor()
        now = datetime.now().isoformat()

        cursor.execute(
            "INSERT INTO matieres (name, class_id, description, teacher_id, created_at) VALUES (?, ?, ?, ?, ?)",
            (subject_name, class_id, description, teacher_id, now)
        )

        conn.commit()
        return True
    except:
        return False

def delete_subject(subject_id):
    """Delete a subject from the local database"""
    try:
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute("DELETE FROM matieres WHERE id = ?", (subject_id,))
        conn.commit()

        return True
    except Exception as e:
        return False

# Attendance-related functions
def save_attendance_to_database(attendance_data, class_id=None, session_date=None, subject_id=None):
    """Save attendance records to local database

    Args:
        attendance_data (dict): Dictionary with student names as keys and attendance status as values
        class_id (str, optional): ID of the class for which attendance is being taken
        session_date (str, optional): Date of the class session in YYYY-MM-DD format.
                                     If None, today's date will be used.
        subject_id (str, optional): ID of the subject (e.g., Math, Computer Science)

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()

        # Create attendance record with timestamp
        timestamp = datetime.now().isoformat()
        time_str = datetime.now().strftime("%H:%M:%S")

        # Use provided session date or today's date
        if session_date:
            date_str = session_date
        else:
            date_str = datetime.now().strftime("%Y-%m-%d")

        # Create attendance records for each student
        skipped_records = 0
        duplicate_records = 0
        saved_records = 0

        for student_name, status in attendance_data.items():
            # Get student ID
            cursor.execute("SELECT id FROM etudiants WHERE name = ?", (student_name,))
            student_row = cursor.fetchone()

            if not student_row:
                skipped_records += 1
                continue

            student_id = student_row['id']

            # Check if attendance record already exists for this student on this date
            query = "SELECT * FROM presences WHERE student_id = ? AND date = ?"
            params = [student_id, date_str]

            # Add class filter if provided
            if class_id:
                query += " AND class_id = ?"
                params.append(class_id)

            # Add subject filter if provided
            if subject_id:
                query += " AND subject_id = ?"
                params.append(subject_id)

            cursor.execute(query, params)
            existing_record = cursor.fetchone()

            if existing_record:
                duplicate_records += 1
                continue

            # Create attendance record
            record_params = [
                student_id,
                1 if status == "Present" else 0,  # Convert to integer for SQLite
                date_str,
                time_str,
                timestamp
            ]

            query = "INSERT INTO presences (student_id, status, date, time, created_at"
            values = "VALUES (?, ?, ?, ?, ?"

            # Add class_id if provided
            if class_id:
                query += ", class_id"
                values += ", ?"
                record_params.append(class_id)

            # Add subject_id if provided
            if subject_id:
                query += ", subject_id"
                values += ", ?"
                record_params.append(subject_id)

            query += ") " + values + ")"

            # Insert each record individually
            try:
                cursor.execute(query, record_params)
                saved_records += 1
            except sqlite3.IntegrityError:
                duplicate_records += 1
            except Exception as insert_error:
                skipped_records += 1

        conn.commit()

        total_processed = saved_records + duplicate_records
        success = total_processed > 0

        return success

    except Exception:
        return False

# Initialize the database when the module is imported
initialize_database()
